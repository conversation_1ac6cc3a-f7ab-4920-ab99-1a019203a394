import 'package:flutter_test/flutter_test.dart';
import 'package:ecoplug/models/wallet/wallet_response.dart';

void main() {
  group('Server Payment Gateway Selection Tests', () {
    test('should extract payment_option from wallet response correctly', () {
      // Test with PhonePe
      final phonePeJson = {
        'wallet': {
          'balance': 100.0,
          'user_id': 1,
          'payment_history': [],
        },
        'payment_option': 'phonepe',
        'success': true,
      };

      final phonePeResponse = WalletResponse.fromJson(phonePeJson);
      expect(phonePeResponse.paymentOption, equals('phonepe'));

      // Test with PayU
      final payUJson = {
        'wallet': {
          'balance': 100.0,
          'user_id': 1,
          'payment_history': [],
        },
        'payment_option': 'payu',
        'success': true,
      };

      final payUResponse = WalletResponse.fromJson(payUJson);
      expect(payUResponse.paymentOption, equals('payu'));

      // Test with Cashfree
      final cashfreeJson = {
        'wallet': {
          'balance': 100.0,
          'user_id': 1,
          'payment_history': [],
        },
        'payment_option': 'cashfree',
        'success': true,
      };

      final cashfreeResponse = WalletResponse.fromJson(cashfreeJson);
      expect(cashfreeResponse.paymentOption, equals('cashfree'));
    });

    test('should handle missing payment_option gracefully', () {
      final json = {
        'wallet': {
          'balance': 100.0,
          'user_id': 1,
          'payment_history': [],
        },
        'success': true,
      };

      final walletResponse = WalletResponse.fromJson(json);
      expect(walletResponse.paymentOption, isNull);
    });

    test('should handle empty payment_option gracefully', () {
      final json = {
        'wallet': {
          'balance': 100.0,
          'user_id': 1,
          'payment_history': [],
        },
        'payment_option': '',
        'success': true,
      };

      final walletResponse = WalletResponse.fromJson(json);
      expect(walletResponse.paymentOption, equals(''));
    });

    test('should handle case-insensitive payment options', () {
      final testCases = [
        'PhonePe',
        'PHONEPE',
        'phonepe',
        'PayU',
        'PAYU',
        'payu',
        'Cashfree',
        'CASHFREE',
        'cashfree',
      ];

      for (final paymentOption in testCases) {
        final json = {
          'wallet': {
            'balance': 100.0,
            'user_id': 1,
            'payment_history': [],
          },
          'payment_option': paymentOption,
          'success': true,
        };

        final walletResponse = WalletResponse.fromJson(json);
        expect(walletResponse.paymentOption, equals(paymentOption));
        
        // Test that the payment option can be normalized to lowercase
        final normalizedOption = walletResponse.paymentOption?.toLowerCase().trim();
        expect(['phonepe', 'payu', 'cashfree'].contains(normalizedOption), isTrue);
      }
    });

    test('should handle unknown payment options', () {
      final json = {
        'wallet': {
          'balance': 100.0,
          'user_id': 1,
          'payment_history': [],
        },
        'payment_option': 'unknown_gateway',
        'success': true,
      };

      final walletResponse = WalletResponse.fromJson(json);
      expect(walletResponse.paymentOption, equals('unknown_gateway'));
    });

    test('should work with complete wallet response structure', () {
      final completeJson = {
        'wallet': {
          'balance': 873.46,
          'user_id': 4,
          'payment_history': [
            {
              'id': 1,
              'amount': 100.0,
              'remark': 'Test payment',
              'created_at': '2024-01-01T00:00:00Z',
              'type': 'cr',
              'status': 'SUCCESS',
              'source': 'phonepe',
            }
          ],
        },
        'payment_option': 'phonepe',
        'offer_message': {
          'title': 'Available Offers',
          'message': 'Add ₹500 or more and get upto 15% extra balance',
          'status': false,
        },
        'success': true,
      };

      final walletResponse = WalletResponse.fromJson(completeJson);
      
      expect(walletResponse.success, isTrue);
      expect(walletResponse.paymentOption, equals('phonepe'));
      expect(walletResponse.wallet?.balance, equals(873.46));
      expect(walletResponse.wallet?.userId, equals(4));
      expect(walletResponse.offerMessage?.title, equals('Available Offers'));
    });
  });

  group('Payment Gateway Selection Logic Tests', () {
    test('should select correct gateway based on payment_option', () {
      // Simulate the if-else logic from _initiateServerDeterminedPayment
      String getSelectedGateway(String? paymentOption) {
        final serverPaymentOption = paymentOption?.toLowerCase().trim() ?? 'payu';
        
        if (serverPaymentOption == 'phonepe') {
          return 'phonepe';
        } else if (serverPaymentOption == 'payu') {
          return 'payu';
        } else if (serverPaymentOption == 'cashfree') {
          return 'cashfree';
        } else {
          return 'payu'; // fallback
        }
      }

      // Test all supported gateways
      expect(getSelectedGateway('phonepe'), equals('phonepe'));
      expect(getSelectedGateway('payu'), equals('payu'));
      expect(getSelectedGateway('cashfree'), equals('cashfree'));
      
      // Test case variations
      expect(getSelectedGateway('PhonePe'), equals('phonepe'));
      expect(getSelectedGateway('PAYU'), equals('payu'));
      expect(getSelectedGateway('CashFree'), equals('cashfree'));
      
      // Test with whitespace
      expect(getSelectedGateway(' phonepe '), equals('phonepe'));
      expect(getSelectedGateway(' payu '), equals('payu'));
      
      // Test fallback scenarios
      expect(getSelectedGateway('unknown'), equals('payu'));
      expect(getSelectedGateway(''), equals('payu'));
      expect(getSelectedGateway(null), equals('payu'));
    });
  });
}
