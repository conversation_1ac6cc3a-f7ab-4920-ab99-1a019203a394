import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'dart:convert';

import '../lib/core/api/api_service.dart';
import '../lib/services/payment/payu_error_handler.dart';
import '../lib/services/payment/payu_transaction_verifier.dart';
import '../lib/services/payment/payu_service.dart';
import '../lib/models/wallet_transaction_model.dart';

// Generate mocks
@GenerateMocks([ApiService])
import 'payu_integration_comprehensive_test.mocks.dart';

void main() {
  group('PayU Integration Comprehensive Tests', () {
    late MockApiService mockApiService;
    late PayUTransactionVerifier verifier;

    setUp(() {
      mockApiService = MockApiService();
      verifier = PayUTransactionVerifier(mockApiService);
    });

    group('PayU Error Handler Tests', () {
      test('should handle initialization errors correctly', () {
        final error = Exception('Invalid merchant key');
        final payuError = PayUErrorHandler.handleInitializationError(error);

        expect(payuError.type, PayUErrorType.configuration);
        expect(payuError.code, 'INVALID_MERCHANT_KEY');
        expect(payuError.isRetryable, false);
        expect(payuError.userMessage, contains('temporarily unavailable'));
      });

      test('should handle network errors with retry capability', () {
        final error = Exception('Connection timeout');
        final payuError = PayUErrorHandler.handleTransactionError(error, 'TXN123');

        expect(payuError.type, PayUErrorType.network);
        expect(payuError.code, 'TRANSACTION_TIMEOUT');
        expect(payuError.isRetryable, true);
        expect(payuError.transactionId, 'TXN123');
      });

      test('should handle user cancellation appropriately', () {
        final error = Exception('Payment cancelled by user');
        final payuError = PayUErrorHandler.handleTransactionError(error, 'TXN456');

        expect(payuError.type, PayUErrorType.userCancelled);
        expect(payuError.code, 'USER_CANCELLED');
        expect(payuError.isRetryable, false);
        expect(payuError.userMessage, 'Payment was cancelled.');
      });

      test('should provide recovery steps for different error types', () {
        final networkError = PayUError(
          type: PayUErrorType.network,
          code: 'CONNECTION_ERROR',
          message: 'Network error',
          userMessage: 'Check connection',
        );

        final recoverySteps = PayUErrorRecovery.getRecoverySteps(networkError);
        expect(recoverySteps, isNotEmpty);
        expect(recoverySteps.first, contains('internet connection'));
      });
    });

    group('PayU Transaction Verifier Tests', () {
      test('should verify successful transaction correctly', () async {
        // Mock successful wallet response
        when(mockApiService.get('/user/wallet/info')).thenAnswer((_) async => {
          'success': true,
          'data': {
            'payment_history': [
              {
                'id': 'TXN123',
                'amount': 100.0,
                'status': 'COMPLETED',
                'type': 'CREDIT',
                'remark': 'Payment successful',
                'created_at': DateTime.now().toIso8601String(),
                'source': 'payu',
              }
            ]
          }
        });

        final result = await verifier.verifyTransaction(
          transactionId: 'TXN123',
          expectedStatus: 'COMPLETED',
        );

        expect(result.isVerified, true);
        expect(result.actualStatus, 'COMPLETED');
        expect(result.transaction, isNotNull);
      });

      test('should handle transaction not found scenario', () async {
        // Mock wallet response with no matching transaction
        when(mockApiService.get('/user/wallet/info')).thenAnswer((_) async => {
          'success': true,
          'data': {
            'payment_history': []
          }
        });

        final result = await verifier.verifyTransaction(
          transactionId: 'NONEXISTENT',
          expectedStatus: 'COMPLETED',
        );

        expect(result.isVerified, false);
        expect(result.actualStatus, 'ERROR');
        expect(result.error, isNotNull);
        expect(result.shouldRetry, true);
      });

      test('should handle API failures gracefully', () async {
        // Mock API failure
        when(mockApiService.get('/user/wallet/info'))
            .thenThrow(Exception('API Error'));

        final result = await verifier.verifyTransaction(
          transactionId: 'TXN789',
          expectedStatus: 'COMPLETED',
        );

        expect(result.isVerified, false);
        expect(result.actualStatus, 'ERROR');
        expect(result.error, isNotNull);
        expect(result.shouldRetry, true);
      });

      test('should perform batch verification correctly', () async {
        // Mock successful responses for multiple transactions
        when(mockApiService.get('/user/wallet/info')).thenAnswer((_) async => {
          'success': true,
          'data': {
            'payment_history': [
              {
                'id': 'TXN001',
                'amount': 100.0,
                'status': 'COMPLETED',
                'type': 'CREDIT',
                'remark': 'Payment 1',
                'created_at': DateTime.now().toIso8601String(),
                'source': 'payu',
              },
              {
                'id': 'TXN002',
                'amount': 200.0,
                'status': 'PENDING',
                'type': 'CREDIT',
                'remark': 'Payment 2',
                'created_at': DateTime.now().toIso8601String(),
                'source': 'payu',
              }
            ]
          }
        });

        final results = await verifier.verifyMultipleTransactions(
          ['TXN001', 'TXN002'],
          expectedStatus: 'COMPLETED',
        );

        expect(results.length, 2);
        expect(results[0].isVerified, true);  // TXN001 matches
        expect(results[1].isVerified, false); // TXN002 is pending
      });
    });

    group('PayU Response Payload Tests', () {
      test('should create standardized payload correctly', () {
        final mockResult = PayUPaymentResult.success({
          'status': 'success',
          'txnid': 'TXN123',
          'amount': '100.00',
          'mihpayid': 'PAYU123',
        });

        // This would be tested in the actual widget/service
        final payload = {
          'status': 'success',
          'txnid': 'TXN123',
          'hash': 'test_hash',
          'response': mockResult.data,
          'client_timestamp': DateTime.now().toIso8601String(),
          'payment_gateway': 'payu',
        };

        expect(payload['status'], 'success');
        expect(payload['txnid'], 'TXN123');
        expect(payload['payment_gateway'], 'payu');
        expect(payload['response'], isNotNull);
      });

      test('should normalize status correctly', () {
        final testCases = {
          'success': 'success',
          'SUCCESS': 'success',
          'completed': 'success',
          'failure': 'failure',
          'FAILED': 'failure',
          'cancelled': 'cancelled',
          'pending': 'pending',
          'unknown_status': 'unknown',
        };

        for (final entry in testCases.entries) {
          final normalized = _normalizeStatus(entry.key);
          expect(normalized, entry.value, 
              reason: 'Status ${entry.key} should normalize to ${entry.value}');
        }
      });
    });

    group('PayU Backend Integration Tests', () {
      test('should handle backend response formats correctly', () {
        // Test string response
        final stringResponse = 'success';
        final processedString = _processBackendResponse(stringResponse);
        expect(processedString['success'], true);
        expect(processedString['status'], 'success');
        expect(processedString['response_type'], 'string');

        // Test JSON response
        final jsonResponse = {
          'success': true,
          'message': 'Payment processed',
          'transaction_id': 'TXN123'
        };
        final processedJson = _processBackendResponse(jsonResponse);
        expect(processedJson['success'], true);
        expect(processedJson['data'], jsonResponse);
        expect(processedJson['response_type'], 'json');
      });

      test('should validate required fields in PayU request', () {
        // Valid request
        final validRequest = {
          'status': 'success',
          'txnid': 'TXN123456',
          'hash': 'test_hash',
          'response': {}
        };
        expect(() => _validatePayURequest(validRequest), returnsNormally);

        // Invalid request - missing status
        final invalidRequest1 = {
          'txnid': 'TXN123456',
          'hash': 'test_hash',
        };
        expect(() => _validatePayURequest(invalidRequest1), throwsException);

        // Invalid request - empty txnid
        final invalidRequest2 = {
          'status': 'success',
          'txnid': '',
          'hash': 'test_hash',
        };
        expect(() => _validatePayURequest(invalidRequest2), throwsException);
      });
    });

    group('PayU Error Recovery Tests', () {
      test('should provide appropriate recovery steps', () {
        final networkError = PayUError(
          type: PayUErrorType.network,
          code: 'CONNECTION_TIMEOUT',
          message: 'Connection timed out',
          userMessage: 'Check your connection',
        );

        final steps = PayUErrorRecovery.getRecoverySteps(networkError);
        expect(steps, contains(matches(RegExp(r'internet.*connection', caseSensitive: false))));
        expect(steps, contains(matches(RegExp(r'try.*again', caseSensitive: false))));
      });

      test('should identify retryable vs non-retryable errors', () {
        final retryableError = PayUError(
          type: PayUErrorType.network,
          code: 'CONNECTION_TIMEOUT',
          message: 'Timeout',
          userMessage: 'Try again',
          isRetryable: true,
        );

        final nonRetryableError = PayUError(
          type: PayUErrorType.validation,
          code: 'INVALID_CARD',
          message: 'Invalid card',
          userMessage: 'Check card details',
          isRetryable: false,
        );

        expect(retryableError.isRetryable, true);
        expect(nonRetryableError.isRetryable, false);
      });
    });
  });
}

// Helper functions for testing
String _normalizeStatus(String status) {
  final statusLower = status.toLowerCase();
  if (['success', 'completed', 'successful'].contains(statusLower)) return 'success';
  if (['failure', 'failed', 'error'].contains(statusLower)) return 'failure';
  if (['cancelled', 'canceled'].contains(statusLower)) return 'cancelled';
  if (['pending', 'processing'].contains(statusLower)) return 'pending';
  return 'unknown';
}

Map<String, dynamic> _processBackendResponse(dynamic response) {
  if (response is String) {
    return {
      'success': true,
      'status': response,
      'response_type': 'string',
    };
  } else if (response is Map<String, dynamic>) {
    return {
      'success': true,
      'data': response,
      'response_type': 'json',
    };
  }
  throw Exception('Invalid response type');
}

void _validatePayURequest(Map<String, dynamic> request) {
  if (!request.containsKey('status') || request['status']?.toString().isEmpty == true) {
    throw Exception('Missing status');
  }
  if (!request.containsKey('txnid') || request['txnid']?.toString().isEmpty == true) {
    throw Exception('Missing txnid');
  }
}

// Mock PayUPaymentResult for testing
class PayUPaymentResult {
  final PayUPaymentResultType type;
  final Map<String, dynamic>? data;
  final String? message;

  PayUPaymentResult._(this.type, this.data, this.message);

  factory PayUPaymentResult.success(Map<String, dynamic> data) {
    return PayUPaymentResult._(PayUPaymentResultType.success, data, null);
  }

  factory PayUPaymentResult.failed(String message) {
    return PayUPaymentResult._(PayUPaymentResultType.failed, null, message);
  }
}

enum PayUPaymentResultType { success, failed, cancelled }
