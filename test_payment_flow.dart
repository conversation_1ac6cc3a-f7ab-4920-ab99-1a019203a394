import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// Import the services we need to test
import 'lib/services/payment/payu_service.dart';
import 'lib/services/payment/payu_lifecycle_manager.dart';
import 'lib/core/api/api_service.dart';

// Generate mocks
@GenerateMocks([ApiService, PayULifecycleManager])
import 'test_payment_flow.mocks.dart';

void main() {
  group('PayU Payment Flow Tests', () {
    late MockApiService mockApiService;
    late MockPayULifecycleManager mockLifecycleManager;

    setUp(() {
      mockApiService = MockApiService();
      mockLifecycleManager = MockPayULifecycleManager();
    });

    test('PayU SDK initialization should succeed', () async {
      // Test PayU SDK initialization
      final result = await PayUService.initialize(
        merchantKey: 'test_merchant_key',
        merchantSalt: 'test_merchant_salt',
        environment: PayUEnvironment.test,
      );

      expect(result, isTrue);
    });

    test('PayU payment callbacks should be properly registered', () {
      // Test that PayU service implements the callback protocol
      final payuService = PayUService.instance;
      
      // Verify that the service implements the required callback methods
      expect(payuService, isA<PayUCheckoutProProtocol>());
    });

    test('Server notification callback should be registered and called', () async {
      bool callbackCalled = false;
      PayUPaymentResult? receivedResult;
      String? receivedTransactionId;

      // Register a test callback
      PayUService.registerServerNotificationCallback((result, transactionId) async {
        callbackCalled = true;
        receivedResult = result;
        receivedTransactionId = transactionId;
      });

      // Simulate a successful payment callback
      final testResult = PayUPaymentResult.success({
        'status': 'success',
        'txnid': 'test_txn_123',
        'amount': '100.0',
      });

      // Trigger the server notification
      await PayUService.instance._notifyServerOfPaymentResult(testResult, 'test_txn_123');

      // Verify callback was called
      expect(callbackCalled, isTrue);
      expect(receivedResult?.type, equals(PayUResultType.success));
      expect(receivedTransactionId, equals('test_txn_123'));
    });

    test('Payment success callback should handle response correctly', () {
      final payuService = PayUService.instance;
      
      // Mock a successful payment response
      final mockResponse = {
        'status': 'success',
        'txnid': 'test_txn_123',
        'amount': '100.0',
        'productinfo': 'Wallet Recharge',
        'hash': 'test_hash_value',
      };

      // This should not throw an exception
      expect(() => payuService.onPaymentSuccess(mockResponse), returnsNormally);
    });

    test('Payment failure callback should handle response correctly', () {
      final payuService = PayUService.instance;
      
      // Mock a failed payment response
      final mockResponse = {
        'status': 'failure',
        'txnid': 'test_txn_123',
        'error': 'Payment failed due to insufficient funds',
      };

      // This should not throw an exception
      expect(() => payuService.onPaymentFailure(mockResponse), returnsNormally);
    });

    test('Payment cancellation callback should handle response correctly', () {
      final payuService = PayUService.instance;
      
      // Mock a cancelled payment response
      final mockResponse = {
        'status': 'cancelled',
        'txnid': 'test_txn_123',
        'error': 'Payment cancelled by user',
      };

      // This should not throw an exception
      expect(() => payuService.onPaymentCancel(mockResponse), returnsNormally);
    });

    test('Payment error callback should handle response correctly', () {
      final payuService = PayUService.instance;
      
      // Mock an error response
      final mockResponse = {
        'status': 'error',
        'txnid': 'test_txn_123',
        'error': 'Network error occurred',
      };

      // This should not throw an exception
      expect(() => payuService.onError(mockResponse), returnsNormally);
    });

    test('App lifecycle resume should trigger payment check', () async {
      // Mock pending payment data
      when(mockLifecycleManager.hasPendingPayment()).thenAnswer((_) async => true);
      when(mockLifecycleManager.getPendingPaymentData()).thenAnswer((_) async => {
        'transactionId': 'test_txn_123',
        'amount': 100.0,
        'merchantTxnId': 'merchant_123',
      });
      when(mockLifecycleManager.handleAppResume()).thenAnswer((_) async => 
        PayUPaymentResult.success({'status': 'success', 'txnid': 'test_txn_123'})
      );

      // Simulate app resume handling
      final result = await mockLifecycleManager.handleAppResume();
      
      expect(result, isNotNull);
      expect(result?.type, equals(PayUResultType.success));
    });

    test('Fallback mechanism should handle missing callbacks', () async {
      // Test the fallback mechanism when PayU callbacks don't fire
      // This simulates the scenario where user returns from payment gateway
      // but PayU SDK callbacks are not triggered
      
      // Mock pending payment without result
      when(mockLifecycleManager.hasPendingPayment()).thenAnswer((_) async => true);
      when(mockLifecycleManager.getPendingPaymentData()).thenAnswer((_) async => {
        'transactionId': 'test_txn_123',
        'amount': 100.0,
      });
      when(mockLifecycleManager.handleAppResume()).thenAnswer((_) async => null);

      // The fallback should handle this gracefully
      final hasPending = await mockLifecycleManager.hasPendingPayment();
      final result = await mockLifecycleManager.handleAppResume();
      
      expect(hasPending, isTrue);
      expect(result, isNull); // No result means fallback should be triggered
    });

    tearDown(() {
      // Clean up after each test
      PayUService.unregisterServerNotificationCallback();
    });
  });

  group('Payment Response Processing Tests', () {
    test('Backend response should be processed correctly', () {
      // Test different types of backend responses
      final stringResponse = 'success';
      final jsonResponse = {'success': true, 'message': 'Payment completed'};
      final errorResponse = {'success': false, 'error': 'Payment failed'};

      // These should all be handled without crashing
      expect(() => _processBackendResponse(stringResponse), returnsNormally);
      expect(() => _processBackendResponse(jsonResponse), returnsNormally);
      expect(() => _processBackendResponse(errorResponse), returnsNormally);
    });
  });
}

// Helper function to simulate backend response processing
void _processBackendResponse(dynamic response) {
  // Simulate the response processing logic
  if (response is String) {
    print('Processing string response: $response');
  } else if (response is Map<String, dynamic>) {
    print('Processing JSON response: $response');
  } else {
    print('Processing unknown response type: ${response.runtimeType}');
  }
}
