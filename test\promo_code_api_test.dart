import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() {
  group('Promo Code API Tests', () {
    test('Test promo codes endpoint with token', () async {
      // Using the provided auth token
      const String testToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOlwvXC9hcGkyLmVlaWwub25saW5lXC9hcGlcL3YxXC91c2VyXC92ZXJpZnktb3RwIiwiaWF0IjoxNzQ0MzYzNTkwLCJleHAiOjYwNjQ0NDk5MzAsIm5iZiI6MTc0NDM2MzU5MCwianRpIjoibzE5Z2FrR29uSWxuNnBUMiIsInN1YiI6NCwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.dqxJf2xGRAqp7RsLpxlOJXzm2u-BFNOHPZf7kV6_Hqo';
      
      print('🧪 Testing promo codes API...');
      print('🔗 URL: https://api2.eeil.online/api/v1/user/promocodes?promo_type=recharge');
      print('🔑 Token: ${testToken.substring(0, 20)}...');
      
      try {
        final response = await http.get(
          Uri.parse('https://api2.eeil.online/api/v1/user/promocodes?promo_type=recharge'),
          headers: {
            'Authorization': 'Bearer $testToken',
          },
        );
        
        print('📡 Response Status: ${response.statusCode}');
        print('📄 Response Headers: ${response.headers}');
        print('📦 Response Body: ${response.body}');
        
        expect(response.statusCode, 200);
        
        final jsonData = json.decode(response.body);
        print('✅ JSON parsed successfully');
        print('🔍 Success field: ${jsonData['success']}');
        print('📊 Data field exists: ${jsonData['data'] != null}');
        
        if (jsonData['data'] != null) {
          final List<dynamic> promoData = jsonData['data'];
          print('📦 Promo codes count: ${promoData.length}');
          
          if (promoData.isNotEmpty) {
            print('🏷️ First promo code: ${promoData[0]}');
          }
        }
        
        expect(jsonData['success'], true);
        expect(jsonData['data'], isNotNull);
        
      } catch (e) {
        print('❌ Error: $e');
        fail('API call failed: $e');
      }
    });
    
    test('Test promo codes endpoint without token', () async {
      print('🧪 Testing promo codes API without token...');
      
      try {
        final response = await http.get(
          Uri.parse('https://api2.eeil.online/api/v1/user/promocodes?promo_type=recharge'),
        );
        
        print('📡 Response Status (no token): ${response.statusCode}');
        print('📄 Response Body (no token): ${response.body}');
        
        // This might return 401 or still work depending on API design
        print('ℹ️ Status without token: ${response.statusCode}');
        
      } catch (e) {
        print('❌ Error without token: $e');
      }
    });
  });
}
