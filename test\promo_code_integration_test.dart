import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ecoplug/screens/wallet/add_balance_sheet.dart';

void main() {
  group('Promo Code Integration Tests', () {
    
    testWidgets('Promo code section should be visible with server offer message', (WidgetTester tester) async {
      // Your server's JSON format
      final offerMessage = {
        "title": "Available Offers",
        "message": "Add ₹500 or more and get upto 15% extra balance",
        "status": false
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddBalanceSheet(
              offerMessage: offerMessage,
              onAddBalance: (amount, {String? source}) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify promo code section is visible
      expect(find.text('Available Offers'), findsOneWidget);
      expect(find.text('Add ₹500 or more and get upto 15% extra balance'), findsOneWidget);
      expect(find.text('Have a promo code?'), findsOneWidget);
    });

    testWidgets('Promo code section should be visible even without server offer', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddBalanceSheet(
              offerMessage: null, // No server offer
              onAddBalance: (amount, {String? source}) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify promo code section is still visible with default text
      expect(find.text('Promo Codes & Offers'), findsOneWidget);
      expect(find.text('Enter a promo code to get extra balance on your recharge'), findsOneWidget);
      expect(find.text('Have a promo code?'), findsOneWidget);
    });

    testWidgets('Should extract percentage from server offer message', (WidgetTester tester) async {
      final offerMessage = {
        "title": "Special Offer",
        "message": "Add ₹1000 or more and get upto 20% extra balance",
        "status": true
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddBalanceSheet(
              offerMessage: offerMessage,
              onAddBalance: (amount, {String? source}) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap to show promo code input
      await tester.tap(find.text('Have a promo code?'));
      await tester.pumpAndSettle();

      // Enter amount and promo code
      await tester.enterText(find.byType(TextField).first, '1000');
      await tester.enterText(find.byType(TextField).last, 'SERVER15');

      // Tap apply button
      await tester.tap(find.text('Apply'));
      await tester.pumpAndSettle();

      // Wait for validation
      await tester.pump(const Duration(seconds: 1));

      // Should show success message
      expect(find.text('Promo Applied Successfully!'), findsOneWidget);
    });

    testWidgets('Should handle different server offer statuses', (WidgetTester tester) async {
      // Test with status: false
      final offerMessageInactive = {
        "title": "Available Offers",
        "message": "Add ₹500 or more and get upto 15% extra balance",
        "status": false
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddBalanceSheet(
              offerMessage: offerMessageInactive,
              onAddBalance: (amount, {String? source}) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should still show promo code section
      expect(find.text('Available Offers'), findsOneWidget);
      expect(find.text('Have a promo code?'), findsOneWidget);

      // Test with status: true
      final offerMessageActive = {
        "title": "Active Offers",
        "message": "Add ₹500 or more and get upto 15% extra balance",
        "status": true
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddBalanceSheet(
              offerMessage: offerMessageActive,
              onAddBalance: (amount, {String? source}) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show promo code section with active offer
      expect(find.text('Active Offers'), findsOneWidget);
      expect(find.text('Have a promo code?'), findsOneWidget);
    });

    testWidgets('Should validate promo codes with server offer format', (WidgetTester tester) async {
      final offerMessage = {
        "title": "Available Offers",
        "message": "Add ₹500 or more and get upto 15% extra balance",
        "status": false
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddBalanceSheet(
              offerMessage: offerMessage,
              onAddBalance: (amount, {String? source}) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap to show promo code input
      await tester.tap(find.text('Have a promo code?'));
      await tester.pumpAndSettle();

      // Test valid promo code
      await tester.enterText(find.byType(TextField).first, '600');
      await tester.enterText(find.byType(TextField).last, 'SAVE15');
      await tester.tap(find.text('Apply'));
      await tester.pumpAndSettle();

      // Wait for validation
      await tester.pump(const Duration(seconds: 1));

      // Should show success
      expect(find.text('Promo Applied Successfully!'), findsOneWidget);

      // Test invalid amount
      await tester.tap(find.text('Remove'));
      await tester.pumpAndSettle();

      await tester.enterText(find.byType(TextField).first, '100');
      await tester.enterText(find.byType(TextField).last, 'SAVE15');
      await tester.tap(find.text('Apply'));
      await tester.pumpAndSettle();

      // Wait for validation
      await tester.pump(const Duration(seconds: 1));

      // Should show error
      expect(find.textContaining('Minimum amount'), findsOneWidget);
    });

    testWidgets('Should show promo bonus calculation', (WidgetTester tester) async {
      final offerMessage = {
        "title": "Available Offers",
        "message": "Add ₹500 or more and get upto 15% extra balance",
        "status": true
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddBalanceSheet(
              offerMessage: offerMessage,
              onAddBalance: (amount, {String? source}) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap to show promo code input
      await tester.tap(find.text('Have a promo code?'));
      await tester.pumpAndSettle();

      // Enter amount and valid promo code
      await tester.enterText(find.byType(TextField).first, '1000');
      await tester.enterText(find.byType(TextField).last, 'SAVE15');
      await tester.tap(find.text('Apply'));
      await tester.pumpAndSettle();

      // Wait for validation
      await tester.pump(const Duration(seconds: 1));

      // Should show bonus amount
      expect(find.textContaining('15% bonus'), findsOneWidget);
      expect(find.textContaining('₹100.00 extra'), findsOneWidget);
    });

    testWidgets('Should handle promo code removal', (WidgetTester tester) async {
      final offerMessage = {
        "title": "Available Offers",
        "message": "Add ₹500 or more and get upto 15% extra balance",
        "status": false
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AddBalanceSheet(
              offerMessage: offerMessage,
              onAddBalance: (amount, {String? source}) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Apply promo code first
      await tester.tap(find.text('Have a promo code?'));
      await tester.pumpAndSettle();

      await tester.enterText(find.byType(TextField).first, '500');
      await tester.enterText(find.byType(TextField).last, 'SAVE15');
      await tester.tap(find.text('Apply'));
      await tester.pumpAndSettle();

      // Wait for validation
      await tester.pump(const Duration(seconds: 1));

      // Should show applied state
      expect(find.text('Remove'), findsOneWidget);

      // Remove promo code
      await tester.tap(find.text('Remove'));
      await tester.pumpAndSettle();

      // Should return to initial state
      expect(find.text('Apply'), findsOneWidget);
      expect(find.text('Promo Applied Successfully!'), findsNothing);
    });

    test('Should extract percentage and amount from server message correctly', () {
      // Test different message formats
      final testCases = [
        {
          'message': 'Add ₹500 or more and get upto 15% extra balance',
          'expectedPercentage': 15.0,
          'expectedMinAmount': 500.0,
        },
        {
          'message': 'Add ₹1000 or more and get upto 20% extra balance',
          'expectedPercentage': 20.0,
          'expectedMinAmount': 1000.0,
        },
        {
          'message': 'Add ₹200 or more and get upto 10% extra balance',
          'expectedPercentage': 10.0,
          'expectedMinAmount': 200.0,
        },
        {
          'message': 'Get 25% bonus on recharge of ₹300 or above',
          'expectedPercentage': 25.0,
          'expectedMinAmount': 300.0,
        },
      ];

      for (final testCase in testCases) {
        final message = testCase['message'] as String;
        
        // Extract percentage
        final percentageMatch = RegExp(r'(\d+)%').firstMatch(message);
        final percentage = percentageMatch != null
            ? double.tryParse(percentageMatch.group(1) ?? '15') ?? 15.0
            : 15.0;
        
        // Extract minimum amount
        final amountMatch = RegExp(r'₹(\d+)').firstMatch(message);
        final minAmount = amountMatch != null
            ? double.tryParse(amountMatch.group(1) ?? '500') ?? 500.0
            : 500.0;

        expect(percentage, equals(testCase['expectedPercentage']),
            reason: 'Failed to extract percentage from: $message');
        expect(minAmount, equals(testCase['expectedMinAmount']),
            reason: 'Failed to extract min amount from: $message');
      }
    });
  });
}
