import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../utils/app_themes.dart';

/// Data model for promo code
class PromoCode {
  final int id;
  final String promoType;
  final int credits;
  final String code;
  final String description;
  final String startDate;
  final String endDate;
  final String frequency;
  final int maxLimit;
  final int userId;
  final int status;
  final String createdAt;
  final String updatedAt;
  final int codeUsed;
  final int redeemed;
  final int minimumAmountApplicable;
  final String companyUid;

  PromoCode({
    required this.id,
    required this.promoType,
    required this.credits,
    required this.code,
    required this.description,
    required this.startDate,
    required this.endDate,
    required this.frequency,
    required this.maxLimit,
    required this.userId,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.codeUsed,
    required this.redeemed,
    required this.minimumAmountApplicable,
    required this.companyUid,
  });

  factory PromoCode.fromJson(Map<String, dynamic> json) {
    return PromoCode(
      id: json['id'] ?? 0,
      promoType: json['promo_type'] ?? '',
      credits: json['credits'] ?? 0,
      code: json['code'] ?? '',
      description: json['description'] ?? '',
      startDate: json['start_date'] ?? '',
      endDate: json['end_date'] ?? '',
      frequency: json['frequency'] ?? '',
      maxLimit: json['max_limit'] ?? 0,
      userId: json['user_id'] ?? 0,
      status: json['status'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      codeUsed: json['code_used'] ?? 0,
      redeemed: json['redeemed'] ?? 0,
      minimumAmountApplicable: json['minimum_amount_applicable'] ?? 0,
      companyUid: json['company_uid'] ?? '',
    );
  }

  bool get isActive {
    final now = DateTime.now();
    final endDateTime = DateTime.tryParse(endDate);

    print('🔍 Checking promo code: $code');
    print('   Status: $status (should be 1)');
    print('   End date string: "$endDate"');
    print('   Parsed end date: $endDateTime');
    print('   Is after now: ${endDateTime?.isAfter(now)}');
    print('   Final isActive: ${status == 1 && (endDateTime?.isAfter(now) ?? false)}');

    return status == 1 && (endDateTime?.isAfter(now) ?? false);
  }

  bool isValidForAmount(double amount) {
    return amount >= minimumAmountApplicable;
  }
}

/// A professional bottom sheet for adding balance to the wallet
/// Features modern UI, smooth validation, and excellent user experience
class AddBalanceSheet extends StatefulWidget {
  /// Callback function when user confirms adding balance
  final Function(double amount, {String source, String? promocode}) onAddBalance;

  /// Offer message data from wallet response
  final Map<String, dynamic>? offerMessage;

  const AddBalanceSheet({
    super.key,
    required this.onAddBalance,
    this.offerMessage,
  });

  @override
  State<AddBalanceSheet> createState() => _AddBalanceSheetState();
}

class _AddBalanceSheetState extends State<AddBalanceSheet>
    with SingleTickerProviderStateMixin {
  // Text controller for the amount input
  final TextEditingController _amountController = TextEditingController();

  // Animation controller for smooth animations
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Minimum amount configuration - REMOVED RESTRICTION
  static const double minimumAmount = 1.0; // Changed from 50.0 to 1.0
  static const double maximumAmount = 50000.0;

  // Quick selection amounts (starting with minimum)
  final List<int> _quickAmounts = [
    50,
    100,
    500,
    1000
  ]; // Removed 1 and 10 rupees options

  // Currently selected quick amount (null if custom amount)
  int? _selectedAmount;

  // Validation state
  String? _errorMessage;

  // Server-determined payment gateway (no user selection needed)
  final String _serverSelectedGateway = 'server_determined';

  // Focus node for the text field
  final FocusNode _focusNode = FocusNode();

  // Promo code input controller
  final TextEditingController _promoCodeController = TextEditingController();

  // Promo code state
  bool _showPromoCode = false;
  bool _isPromoApplied = false;
  bool _isPromoLoading = false;
  String? _appliedPromoCode;
  double _promoBonus = 0.0;
  double _promoPercentage = 0.0;

  // Promo code selection state
  List<PromoCode> _availablePromoCodes = [];
  bool _isLoadingPromoCodes = false;
  String? _promoCodeError;
  PromoCode? _selectedPromoCode;

  // Promo code verification state
  Map<String, dynamic>? _verifiedPromo;
  int _minAmountApplicable = 0;
  bool _isVerifyingPromo = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Initialize animations
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
    ));

    // Start the animation
    _animationController.forward();

    // Add listener for amount validation
    _amountController.addListener(_validateAmount);

    // Focus listener for UI updates
    _focusNode.addListener(() {
      setState(() {
        // Trigger rebuild for focus-dependent styling
      });
    });
  }

  @override
  void dispose() {
    _amountController.removeListener(_validateAmount);
    _amountController.dispose();
    _promoCodeController.dispose();
    _animationController.dispose();
    _focusNode.dispose();
    super.dispose();
  }



  // Smooth, professional validation without jarring feedback
  void _validateAmount() {
    final text = _amountController.text.trim();

    if (text.isEmpty) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = null;
      });
      return;
    }

    final amount = double.tryParse(text);

    if (amount == null) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = 'Please enter a valid number';
      });
      return;
    }

    if (amount < minimumAmount) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = null; // Don't show error immediately for better UX
      });
      return;
    }

    if (amount > maximumAmount) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = 'Maximum amount is ₹${maximumAmount.toInt()}';
      });
      return;
    }

    // Amount is valid
    setState(() {
      _errorMessage = null;

      // Check if matches quick amount
      final amountInt = amount.toInt();
      if (amount == amountInt && _quickAmounts.contains(amountInt)) {
        _selectedAmount = amountInt;
      } else {
        _selectedAmount = null;
      }
    });
  }

  // Handle quick amount selection with smooth animation
  void _selectQuickAmount(int amount) {
    setState(() {
      _selectedAmount = amount;
      _amountController.text = amount.toString();
      _errorMessage = null;
    });

    // Smooth unfocus
    _focusNode.unfocus();
  }

  // Professional validation and submission with inline feedback
  void _handleAddBalance() {
    final text = _amountController.text.trim();

    if (text.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter an amount';
      });
      return;
    }

    final amount = double.tryParse(text);

    if (amount == null) {
      setState(() {
        _errorMessage = 'Please enter a valid amount';
      });
      return;
    }

    if (amount < minimumAmount) {
      setState(() {
        _errorMessage = 'Minimum amount is ₹${minimumAmount.toInt()}';
      });
      return;
    }

    if (amount > maximumAmount) {
      setState(() {
        _errorMessage = 'Maximum amount is ₹${maximumAmount.toInt()}';
      });
      return;
    }

    // Clear any error messages
    setState(() {
      _errorMessage = null;
    });

    // Calculate final amount including promo bonus
    final finalAmount = amount + _promoBonus;

    // Success - proceed with payment (server will determine payment gateway)
    // Pass both original amount and promo details
    widget.onAddBalance(
      amount, // Original amount for payment
      source: _serverSelectedGateway,
      promocode: _verifiedPromo?['code'], // Include verified promo code if available
    );
  }



  // Get input background color with enhanced contrast
  Color _getInputBackgroundColor(bool isDarkMode) {
    if (_focusNode.hasFocus) {
      return const Color(0xFF4776E6).withValues(alpha: 0.05); // Use blue background when focused
    }
    // Default background colors for non-focused state
    return isDarkMode ? AppThemes.darkCard : Colors.grey.shade50;
  }

  // Build custom input box for amount entry with solid blue border
  Widget _buildCustomInputBox(bool isDarkMode) {
    return Container(
      height: 58, // Fixed height
      decoration: BoxDecoration(
        color: _getInputBackgroundColor(isDarkMode),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _focusNode.hasFocus
              ? const Color(0xFF4776E6) // Solid blue when focused
              : (isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300), // Gray when not focused
          width: 2.0, // Consistent border width
        ),
      ),
      child: Row(
        children: [
          // Rupee symbol container
          Container(
            width: 60,
            alignment: Alignment.center,
            child: Text(
              '₹',
              style: TextStyle(
                color: _focusNode.hasFocus
                    ? const Color(0xFF4776E6) // Blue when focused
                    : (isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600), // Gray when not focused
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Text input field
          Expanded(
            child: TextField(
              controller: _amountController,
              focusNode: _focusNode,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
              style: TextStyle(
                color: isDarkMode ? AppThemes.darkTextPrimary : const Color(0xFF1A1A1A),
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              decoration: InputDecoration(
                hintText: 'Enter amount (₹${minimumAmount.toInt()} or more)',
                hintStyle: TextStyle(
                  color: isDarkMode ? Colors.grey.shade500 : Colors.grey.shade500,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none, // Remove all borders
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedErrorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 0,
                  vertical: 16,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16), // Right padding
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    // Use app's primary lime color consistently
    final primaryColor = AppThemes.primaryColor; // Lime green #8cc051
    final secondaryColor = AppThemes.secondaryColor; // Blue #3D7AF5
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final screenHeight = MediaQuery.of(context).size.height;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: screenHeight * 0.9,
          ),
          decoration: BoxDecoration(
            color: isDarkMode ? AppThemes.darkSurface : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(28)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: isDarkMode ? 0.3 : 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Scrollable content area
                Flexible(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.only(
                      left: 24,
                      right: 24,
                      top: 16,
                      bottom: keyboardHeight > 0 ? 16 : 0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Modern sheet handle
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: Center(
                            child: Container(
                              width: 48,
                              height: 4,
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? Colors.grey.shade600
                                    : Colors.grey.shade400,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 28),

                        // Professional title
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              children: [
                                Text(
                                  'Add Balance',
                                  style: TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.w700,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF1A1A1A),
                                    letterSpacing: -0.5,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Add money to your wallet securely',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: isDarkMode
                                        ? AppThemes.darkTextSecondary
                                        : Colors.grey.shade600,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 32),

                        // Professional amount input
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Enter Amount',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF333333),
                                  ),
                                ),
                                const SizedBox(height: 12),
                                // Custom input box with solid blue border when focused
                                _buildCustomInputBox(isDarkMode),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 28),

                        // Modern quick amount selection
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Quick Select',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF333333),
                                  ),
                                ),
                                const SizedBox(height: 12),
                                Wrap(
                                  spacing: 12,
                                  runSpacing: 12,
                                  children: _quickAmounts.map((amount) {
                                    final isSelected =
                                        _selectedAmount == amount;
                                    return _buildModernQuickAmountButton(
                                      amount: amount,
                                      isSelected: isSelected,
                                      onTap: () => _selectQuickAmount(amount),
                                      primaryColor: primaryColor,
                                      secondaryColor: secondaryColor,
                                      isDarkMode: isDarkMode,
                                    );
                                  }).toList(),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 32),

                        // Promo code section - always visible
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: _buildPromoCodeSection(isDarkMode, primaryColor, secondaryColor),
                          ),
                        ),

                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ),

                // Fixed bottom section with validation and button
                Container(
                  padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
                  decoration: BoxDecoration(
                    color: isDarkMode ? AppThemes.darkSurface : Colors.white,
                    border: Border(
                      top: BorderSide(
                        color: isDarkMode
                            ? AppThemes.darkBorder
                            : Colors.grey.shade200,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Validation message area (always present but conditionally visible)
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        height: _errorMessage != null ? 40 : 0,
                        child: _errorMessage != null
                            ? Container(
                                width: double.infinity,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? Colors.red.shade900
                                          .withValues(alpha: 0.2)
                                      : Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.red.shade400,
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.warning_amber_rounded,
                                      color: Colors.red.shade600,
                                      size: 18,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        _errorMessage!,
                                        style: TextStyle(
                                          color: isDarkMode
                                              ? Colors.red.shade300
                                              : Colors.red.shade700,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : const SizedBox.shrink(),
                      ),

                      const SizedBox(height: 16),

                      // Show total amount if promo is applied
                      if (_isPromoApplied && _promoBonus > 0)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                primaryColor.withValues(alpha: 0.1),
                                secondaryColor.withValues(alpha: 0.1),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: primaryColor.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Payment Amount: ₹${_amountController.text}',
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? AppThemes.darkTextSecondary
                                          : Colors.grey.shade600,
                                      fontSize: 14,
                                    ),
                                  ),
                                  Text(
                                    'Bonus Amount: +₹${_promoBonus.toStringAsFixed(2)}',
                                    style: TextStyle(
                                      color: Colors.green.shade600,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    'Total Balance',
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? AppThemes.darkTextSecondary
                                          : Colors.grey.shade600,
                                      fontSize: 12,
                                    ),
                                  ),
                                  Text(
                                    '₹${(double.tryParse(_amountController.text) ?? 0 + _promoBonus).toStringAsFixed(2)}',
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? AppThemes.darkTextPrimary
                                          : const Color(0xFF1A1A1A),
                                      fontSize: 18,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                      const SizedBox(height: 16),

                      // Always visible add balance button
                      SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: _buildModernAddBalanceButton(
                              primaryColor, secondaryColor, isDarkMode),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Modern quick amount button with professional styling
  Widget _buildModernQuickAmountButton({
    required int amount,
    required bool isSelected,
    required VoidCallback onTap,
    required Color primaryColor,
    required Color secondaryColor,
    required bool isDarkMode,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
        padding: const EdgeInsets.symmetric(
            horizontal: 18,
            vertical: 10), // Reduced padding for more compact design
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [primaryColor, secondaryColor],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected
              ? null
              : (isDarkMode ? AppThemes.darkCard : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Colors.transparent
                : (isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300),
            width: 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Text(
          '₹$amount',
          style: TextStyle(
            color: isSelected
                ? Colors.white
                : (isDarkMode
                    ? AppThemes.darkTextPrimary
                    : const Color(0xFF333333)),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }





  // Modern add balance button - always visible with professional styling
  Widget _buildModernAddBalanceButton(
      Color primaryColor, Color secondaryColor, bool isDarkMode) {
    // Button is always enabled but shows different states
    final canProceed = _amountController.text.isNotEmpty;

return Container(
      height: 52, // Reduced from 56 to 52 for more compact design
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [secondaryColor, secondaryColor], // Use only blue
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handleAddBalance(),
          borderRadius: BorderRadius.circular(16),
          splashColor: Colors.white.withValues(alpha: 0.2),
          highlightColor: Colors.white.withValues(alpha: 0.1),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (!canProceed) ...[
                  Icon(
                    Icons.info_outline,
                    color: Colors.white.withValues(alpha: 0.8),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  canProceed ? 'Add Balance' : 'Enter Amount to Continue',
                  style: TextStyle(
                    color: canProceed
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.9),
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    letterSpacing: -0.2,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Get appropriate icon based on offer status and type
  IconData _getOfferIcon(bool hasServerOffer, bool offerStatus) {
    if (hasServerOffer && offerStatus) {
      return Icons.local_offer_rounded; // Active server offer
    } else if (hasServerOffer && !offerStatus) {
      return Icons.discount_outlined; // Inactive server offer
    } else {
      return Icons.confirmation_number_outlined; // Default promo code icon
    }
  }

  // Build promo code section with professional UI
  Widget _buildPromoCodeSection(bool isDarkMode, Color primaryColor, Color secondaryColor) {
    // Always show promo code section, use server offer message if available
    final hasServerOffer = widget.offerMessage != null;
    final offerTitle = hasServerOffer
        ? (widget.offerMessage!['title'] ?? 'Available Offers')
        : 'Promo Codes & Offers';
    final offerMessage = hasServerOffer
        ? (widget.offerMessage!['message'] ?? 'Add money to get extra balance')
        : 'Enter a promo code to get extra balance on your recharge';

    // Extract offer status for visual indication
    final offerStatus = hasServerOffer ? (widget.offerMessage!['status'] ?? false) : false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Enhanced offer banner card with improved visual design
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            // Enhanced gradient with lime color theme
            gradient: LinearGradient(
              colors: [
                primaryColor.withValues(alpha: 0.08),
                primaryColor.withValues(alpha: 0.12),
                secondaryColor.withValues(alpha: 0.06),
              ],
              stops: const [0.0, 0.6, 1.0],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: primaryColor.withValues(alpha: 0.25),
              width: 1.5,
            ),
            // Subtle shadow for depth
            boxShadow: [
              BoxShadow(
                color: primaryColor.withValues(alpha: 0.08),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              // Enhanced icon container with better visual hierarchy
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      primaryColor,
                      primaryColor.withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  // Enhanced shadow for the icon container
                  boxShadow: [
                    BoxShadow(
                      color: primaryColor.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  _getOfferIcon(hasServerOffer, offerStatus),
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Enhanced title with better typography
                    Text(
                      offerTitle,
                      style: TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.w700,
                        color: isDarkMode
                            ? AppThemes.darkTextPrimary
                            : const Color(0xFF1A1A1A),
                        letterSpacing: -0.2,
                      ),
                    ),
                    const SizedBox(height: 6),
                    // Enhanced message with better readability
                    Text(
                      offerMessage,
                      style: TextStyle(
                        fontSize: 14,
                        color: isDarkMode
                            ? AppThemes.darkTextSecondary
                            : Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                        height: 1.3,
                      ),
                    ),
                    // Add status indicator if server offer is active
                    if (hasServerOffer && offerStatus) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: primaryColor.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: primaryColor.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.verified_rounded,
                              color: primaryColor,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Active Offer',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Add a subtle arrow indicator
              Icon(
                Icons.keyboard_arrow_down_rounded,
                color: primaryColor.withValues(alpha: 0.6),
                size: 24,
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Promo code input section
        AnimatedSize(
          duration: const Duration(milliseconds: 300),
          child: Column(
            children: [
              // Toggle button for promo code
              if (!_showPromoCode)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _showPromoCode = true;
                    });
                    // Directly open the promo code selection sheet
                    _showPromoCodeSelection();
                  },
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    decoration: BoxDecoration(
                      // Enhanced background with lime theme
                      color: isDarkMode
                          ? AppThemes.darkCard.withValues(alpha: 0.6)
                          : primaryColor.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(
                        color: primaryColor.withValues(alpha: 0.2),
                        width: 1.5,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.redeem_rounded, // More appropriate icon for promo codes
                          color: primaryColor,
                          size: 22,
                        ),
                        const SizedBox(width: 10),
                        Text(
                          'Have a promo code?',
                          style: TextStyle(
                            color: primaryColor,
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            letterSpacing: -0.1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Promo code input field with inline apply button
              if (_showPromoCode)
                Column(
                  children: [
                    const SizedBox(height: 16),

                    // Horizontal row with input field and apply button
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Promo code input field (takes most of the space)
                        Expanded(
                          flex: 3,
                          child: GestureDetector(
                            onTap: _isPromoApplied ? null : _showPromoCodeSelection,
                            child: AbsorbPointer(
                              absorbing: _isPromoApplied,
                              child: TextField(
                                controller: _promoCodeController,
                                enabled: !_isPromoApplied,
                                readOnly: true, // Make it read-only to handle tap
                                textCapitalization: TextCapitalization.characters,
                                style: TextStyle(
                                  color: isDarkMode
                                      ? AppThemes.darkTextPrimary
                                      : const Color(0xFF1A1A1A),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                                decoration: InputDecoration(
                              hintText: _isPromoApplied
                                  ? (_selectedPromoCode != null
                                      ? '${_selectedPromoCode!.code} - ₹${_selectedPromoCode!.credits} bonus'
                                      : 'Promo code applied')
                                  : 'Tap to select promo code',
                              hintStyle: TextStyle(
                                color: _isPromoApplied
                                    ? Colors.green.shade600
                                    : (isDarkMode
                                        ? Colors.grey.shade500
                                        : Colors.grey.shade500),
                                fontSize: 14,
                                fontWeight: _isPromoApplied ? FontWeight.w500 : FontWeight.normal,
                              ),
                              filled: true,
                              fillColor: isDarkMode
                                  ? AppThemes.darkCard
                                  : Colors.grey.shade50,
                              prefixIcon: Icon(
                                Icons.confirmation_number_outlined,
                                color: _isPromoApplied
                                    ? Colors.green
                                    : (isDarkMode
                                        ? Colors.grey.shade500
                                        : Colors.grey.shade600),
                                size: 20,
                              ),
                              suffixIcon: _isPromoApplied
                                  ? Icon(
                                      Icons.check_circle,
                                      color: Colors.green,
                                      size: 20,
                                    )
                                  : Icon(
                                      Icons.keyboard_arrow_down,
                                      color: isDarkMode
                                          ? Colors.grey.shade500
                                          : Colors.grey.shade600,
                                      size: 24,
                                    ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: _isPromoApplied
                                      ? Colors.green
                                      : (isDarkMode
                                          ? AppThemes.darkBorder
                                          : Colors.grey.shade300),
                                  width: 1.5,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: _isPromoApplied
                                      ? Colors.green
                                      : (isDarkMode
                                          ? AppThemes.darkBorder
                                          : AppThemes.primaryColor),
                                  width: 1.5,
                                ),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 14,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: _isPromoApplied
                                      ? Colors.green
                                      : (isDarkMode
                                          ? AppThemes.darkBorder
                                          : Colors.grey.shade300),
                                  width: 1.5,
                                ),
                              ),
                            ),
                              )
                            
                             ),
                            ),
  
                        ),
  
                        // Compact Apply/Remove button with enhanced responsiveness
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: (_isPromoLoading || _isVerifyingPromo)
                                  ? null
                                  : (_isPromoApplied ? _removePromoCode : _applyPromoCode),
                              borderRadius: BorderRadius.circular(12),
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                height: 52, // Match input field height
                                width: (_isPromoLoading || _isVerifyingPromo) ? 52 : 80, // Square when loading, compact when not
                                decoration: BoxDecoration(
                                  gradient: _isPromoApplied
                                      ? LinearGradient(
                                          colors: [
                                            Colors.red.shade400,
                                            Colors.red.shade600,
                                          ],
                                        )
                                      : LinearGradient(
                                          colors: [primaryColor, primaryColor],
                                        ),
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: (_isPromoApplied ? Colors.red : primaryColor)
                                          .withValues(alpha: 0.3),
                                      blurRadius: (_isPromoLoading || _isVerifyingPromo) ? 4 : 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Center(
                                  child: (_isPromoLoading || _isVerifyingPromo)
                                      ? SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                          ),
                                        )
                                      : Text(
                                          _isPromoApplied ? 'Remove' : (_isVerifyingPromo ? 'Verifying...' : 'Apply'),
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 13,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                      ),

                              ),
                            ),
                          ),
                        ),
                      ],
                    ), // End of Row

                    // Promo applied success message
                    if (_isPromoApplied && _promoBonus > 0)
                      Container(
                        margin: const EdgeInsets.only(top: 12),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.green.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.celebration_rounded,
                              color: Colors.green.shade700,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Promo Applied Successfully!',
                                    style: TextStyle(
                                      color: Colors.green.shade700,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    _selectedPromoCode != null
                                        ? 'You will get ₹${_promoBonus.toStringAsFixed(2)} extra bonus'
                                        : 'You will get ₹${_promoBonus.toStringAsFixed(2)} extra (${_promoPercentage.toStringAsFixed(0)}% bonus)',
                                    style: TextStyle(
                                      color: Colors.green.shade600,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
            ],
          ),
        ),
      ],
    );
  }

  // Apply promo code logic with server verification
  Future<void> _applyPromoCode() async {
    final promoCode = _promoCodeController.text.trim().toUpperCase();
    if (promoCode.isEmpty) {
      return;
    }

    final amountText = _amountController.text.trim();
    if (amountText.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter amount first';
      });
      return;
    }

    final amount = double.tryParse(amountText) ?? 0;
    if (amount < minimumAmount) {
      setState(() {
        _errorMessage = 'Minimum amount is ₹${minimumAmount.toInt()}';
      });
      return;
    }

    // Check if promo is already verified and amount meets minimum requirement
    if (_verifiedPromo != null && _verifiedPromo!['code'] == promoCode) {
      if (amount >= _minAmountApplicable) {
        _applyVerifiedPromoCode();
        return;
      } else {
        setState(() {
          _errorMessage = 'Minimum amount required: ₹$_minAmountApplicable';
        });
        return;
      }
    }

    // Verify the promo code with server
    await _verifyPromoCode(promoCode);

    // After verification, check if amount meets minimum requirement
    if (_verifiedPromo != null) {
      if (amount >= _minAmountApplicable) {
        // Promo code is already applied in _applyVerifiedPromoCode
      } else {
        setState(() {
          _errorMessage = 'Minimum amount required: ₹$_minAmountApplicable';
          _isPromoApplied = false;
          _appliedPromoCode = null;
          _promoBonus = 0.0;
        });
      }
    }
  }



  // Remove promo code
  void _removePromoCode() {
    setState(() {
      _isPromoApplied = false;
      _appliedPromoCode = null;
      _promoBonus = 0.0;
      _promoPercentage = 0.0;
      _selectedPromoCode = null;
      _verifiedPromo = null;
      _minAmountApplicable = 0;
      _promoCodeController.clear();
    });
  }

  // Verify promo code with server
  Future<void> _verifyPromoCode(String promoCode) async {
    // Only verify if promo code length > 3 characters
    if (promoCode.length <= 3) {
      setState(() {
        _promoCodeError = 'Promo code must be more than 3 characters';
      });
      return;
    }

    // Set loading state
    setState(() {
      _isVerifyingPromo = true;
      _promoCodeError = null;
    });

    try {
      // Get auth token
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      // Make HTTP POST request to verify endpoint
      final response = await http.post(
        Uri.parse('https://api2.eeil.online/api/v1/promocodes/verify'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'promo': promoCode.toUpperCase(),
        }),
      );

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        // Success response - store verified promo data
        setState(() {
          _verifiedPromo = responseData['data'];
          _minAmountApplicable = _verifiedPromo?['minimum_amount_applicable'] ?? 0;
          _isVerifyingPromo = false;
          _promoCodeError = null;
        });

        // Close any promo selection modals
        if (mounted && Navigator.canPop(context)) {
          Navigator.pop(context);
        }

        // Apply the verified promo code
        _applyVerifiedPromoCode();

      } else {
        // Error response
        setState(() {
          _isVerifyingPromo = false;
          _promoCodeError = responseData['message'] ?? 'Invalid promo code';
          _verifiedPromo = null;
          _minAmountApplicable = 0;
        });
      }
    } catch (e) {
      // Network or other errors
      setState(() {
        _isVerifyingPromo = false;
        _promoCodeError = 'Network error. Please check your connection and try again.';
        _verifiedPromo = null;
        _minAmountApplicable = 0;
      });
    }
  }

  // Apply verified promo code to the UI
  void _applyVerifiedPromoCode() {
    if (_verifiedPromo == null) return;

    setState(() {
      _isPromoApplied = true;
      _appliedPromoCode = _verifiedPromo!['code'];
      _promoCodeController.text = _verifiedPromo!['code'];
      _promoBonus = (_verifiedPromo!['credits'] ?? 0).toDouble();
      _promoPercentage = 0; // Not used for server-verified promo codes
    });
  }

  // Fetch available promo codes from API
  Future<void> _fetchPromoCodes() async {
    print('🔄 Fetching promo codes from API...');

    setState(() {
      _isLoadingPromoCodes = true;
      _promoCodeError = null;
    });

    try {
      // Get auth token
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      print('🌐 Making API request to: https://api2.eeil.online/api/v1/user/promocodes?promo_type=recharge');
      print('🔑 Auth token available: ${token != null}');

      final response = await http.get(
        Uri.parse('https://api2.eeil.online/api/v1/user/promocodes?promo_type=recharge'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      print('📡 Response status code: ${response.statusCode}');
      print('📄 Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        print('✅ JSON decoded successfully');
        print('🔍 Success: ${jsonData['success']}');
        print('📊 Data available: ${jsonData['data'] != null}');

        if (jsonData['success'] == true && jsonData['data'] != null) {
          final List<dynamic> promoData = jsonData['data'];
          print('📦 Raw promo data count: ${promoData.length}');

          final allPromoCodes = promoData.map((item) => PromoCode.fromJson(item)).toList();
          print('🏷️ Parsed promo codes count: ${allPromoCodes.length}');

          final activePromoCodes = allPromoCodes.where((promo) => promo.isActive).toList();
          print('✨ Active promo codes count: ${activePromoCodes.length}');

          setState(() {
            _availablePromoCodes = activePromoCodes;
            _isLoadingPromoCodes = false;
          });
        } else {
          print('❌ API response indicates failure or no data');
          setState(() {
            _promoCodeError = jsonData['message'] ?? 'Failed to load promo codes';
            _isLoadingPromoCodes = false;
          });
        }
      } else {
        print('❌ HTTP error: ${response.statusCode}');
        setState(() {
          _promoCodeError = 'Failed to load promo codes. Status: ${response.statusCode}';
          _isLoadingPromoCodes = false;
        });
      }
    } catch (e) {
      print('💥 Exception occurred: $e');
      setState(() {
        _promoCodeError = 'Network error: $e';
        _isLoadingPromoCodes = false;
      });
    }
  }

  // Show promo code selection bottom sheet
  void _showPromoCodeSelection() {
    print('🎫 Opening promo code selection sheet...');

    // Fetch promo codes when opening the sheet
    _fetchPromoCodes();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildPromoCodeBottomSheet(),
    );
  }

  // Build promo code selection bottom sheet
  Widget _buildPromoCodeBottomSheet() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenHeight = MediaQuery.of(context).size.height;

    return Container(
      height: screenHeight * 0.85,
      decoration: BoxDecoration(
        color: isDarkMode ? AppThemes.darkCard : Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade200,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Available Promo Codes',
                    style: TextStyle(
                      color: isDarkMode ? AppThemes.darkTextPrimary : Colors.black,
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.close,
                    color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: _buildPromoCodeContent(isDarkMode),
          ),
        ],
      ),
    );
  }

  // Build promo code content based on state
  Widget _buildPromoCodeContent(bool isDarkMode) {
    if (_isLoadingPromoCodes) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading promo codes...'),
          ],
        ),
      );
    }

    if (_promoCodeError != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              _promoCodeError!,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchPromoCodes,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_availablePromoCodes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_offer_outlined,
              size: 64,
              color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No promo codes available',
              style: TextStyle(
                color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Manual entry option
        Container(
          margin: const EdgeInsets.all(16),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _showManualEntry(),
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isDarkMode ? AppThemes.darkCard : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade200,
                    width: 1.5,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.edit_outlined,
                      color: AppThemes.primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Enter Code Manually',
                            style: TextStyle(
                              color: isDarkMode ? AppThemes.darkTextPrimary : Colors.black,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Type your promo code directly',
                            style: TextStyle(
                              color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade400,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        // Divider
        if (_availablePromoCodes.isNotEmpty)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(child: Divider(color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300)),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    'OR SELECT FROM AVAILABLE',
                    style: TextStyle(
                      color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade500,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Expanded(child: Divider(color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300)),
              ],
            ),
          ),

        // Available promo codes list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _availablePromoCodes.length,
            itemBuilder: (context, index) {
              final promoCode = _availablePromoCodes[index];
              return _buildPromoCodeCard(promoCode, isDarkMode);
            },
          ),
        ),
      ],
    );
  }

  // Build individual promo code card
  Widget _buildPromoCodeCard(PromoCode promoCode, bool isDarkMode) {
    final currentAmount = double.tryParse(_amountController.text) ?? 0;
    final isValidForAmount = promoCode.isValidForAmount(currentAmount);
    final isDisabled = !isValidForAmount || currentAmount == 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isDisabled ? null : () => _selectPromoCode(promoCode),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isDisabled
                  ? (isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100)
                  : (isDarkMode ? AppThemes.darkCard : Colors.white),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDisabled
                    ? (isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300)
                    : (isDarkMode ? AppThemes.darkBorder : Colors.grey.shade200),
                width: 1.5,
              ),
              boxShadow: isDisabled ? null : [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with code and credits
                Row(
                  children: [
                    // Promo code
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: isDisabled
                            ? Colors.grey.shade400
                            : AppThemes.primaryColor,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        promoCode.code,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Credits/bonus
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isDisabled
                            ? Colors.grey.shade300
                            : Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '₹${promoCode.credits}',
                        style: TextStyle(
                          color: isDisabled
                              ? Colors.grey.shade600
                              : Colors.green.shade700,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Description
                Text(
                  promoCode.description,
                  style: TextStyle(
                    color: isDisabled
                        ? Colors.grey.shade500
                        : (isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade700),
                    fontSize: 14,
                    height: 1.4,
                  ),
                ),

                const SizedBox(height: 12),

                // Details row
                Row(
                  children: [
                    // Minimum amount
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Min Amount',
                            style: TextStyle(
                              color: Colors.grey.shade500,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '₹${promoCode.minimumAmountApplicable}',
                            style: TextStyle(
                              color: isDisabled
                                  ? Colors.grey.shade500
                                  : (isDarkMode ? AppThemes.darkTextPrimary : Colors.black),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Usage count
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Used',
                            style: TextStyle(
                              color: Colors.grey.shade500,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '${promoCode.codeUsed} times',
                            style: TextStyle(
                              color: isDisabled
                                  ? Colors.grey.shade500
                                  : (isDarkMode ? AppThemes.darkTextPrimary : Colors.black),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Expiry
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Expires',
                            style: TextStyle(
                              color: Colors.grey.shade500,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            _formatDate(promoCode.endDate),
                            style: TextStyle(
                              color: isDisabled
                                  ? Colors.grey.shade500
                                  : (isDarkMode ? AppThemes.darkTextPrimary : Colors.black),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // Disabled reason
                if (isDisabled && currentAmount > 0)
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 16,
                          color: Colors.orange.shade700,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Minimum amount required: ₹${promoCode.minimumAmountApplicable}',
                            style: TextStyle(
                              color: Colors.orange.shade700,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Select a promo code and verify it
  void _selectPromoCode(PromoCode promoCode) {
    setState(() {
      _selectedPromoCode = promoCode;
    });

    // Verify the selected promo code with server
    _verifyPromoCode(promoCode.code);
  }

  // Format date string for display
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'Invalid date';
    }
  }

  // Show manual entry dialog
  void _showManualEntry() {
    Navigator.pop(context); // Close the bottom sheet first

    showDialog(
      context: context,
      builder: (context) {
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;
        final manualController = TextEditingController();

        return AlertDialog(
          backgroundColor: isDarkMode ? AppThemes.darkCard : Colors.white,
          title: Text(
            'Enter Promo Code',
            style: TextStyle(
              color: isDarkMode ? AppThemes.darkTextPrimary : Colors.black,
            ),
          ),
          content: TextField(
            controller: manualController,
            textCapitalization: TextCapitalization.characters,
            decoration: InputDecoration(
              hintText: 'Enter promo code',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: isDarkMode ? AppThemes.darkTextSecondary : Colors.grey.shade600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                final code = manualController.text.trim().toUpperCase();
                if (code.isNotEmpty) {
                  Navigator.pop(context); // Close dialog first
                  _verifyPromoCode(code); // Verify the manually entered code
                }
              },
              child: const Text('Verify'),
            ),
          ],
        );
      },
    );
  }
}
