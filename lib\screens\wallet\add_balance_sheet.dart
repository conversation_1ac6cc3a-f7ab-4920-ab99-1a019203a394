import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/app_themes.dart';

/// A professional bottom sheet for adding balance to the wallet
/// Features modern UI, smooth validation, and excellent user experience
class AddBalanceSheet extends StatefulWidget {
  /// Callback function when user confirms adding balance
  final Function(double amount, {String source}) onAddBalance;

  /// Offer message data from wallet response
  final Map<String, dynamic>? offerMessage;

  const AddBalanceSheet({
    super.key,
    required this.onAddBalance,
    this.offerMessage,
  });

  @override
  State<AddBalanceSheet> createState() => _AddBalanceSheetState();
}

class _AddBalanceSheetState extends State<AddBalanceSheet>
    with SingleTickerProviderStateMixin {
  // Text controller for the amount input
  final TextEditingController _amountController = TextEditingController();

  // Animation controller for smooth animations
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Minimum amount configuration - REMOVED RESTRICTION
  static const double minimumAmount = 1.0; // Changed from 50.0 to 1.0
  static const double maximumAmount = 50000.0;

  // Quick selection amounts (starting with minimum)
  final List<int> _quickAmounts = [
    50,
    100,
    500,
    1000
  ]; // Removed 1 and 10 rupees options

  // Currently selected quick amount (null if custom amount)
  int? _selectedAmount;

  // Validation state
  String? _errorMessage;

  // Server-determined payment gateway (no user selection needed)
  final String _serverSelectedGateway = 'server_determined';

  // Focus node for the text field
  final FocusNode _focusNode = FocusNode();

  // Promo code input controller
  final TextEditingController _promoCodeController = TextEditingController();

  // Promo code state
  bool _showPromoCode = false;
  bool _isPromoApplied = false;
  bool _isPromoLoading = false;
  String? _appliedPromoCode;
  double _promoBonus = 0.0;
  double _promoPercentage = 0.0;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Initialize animations
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOutCubic),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
    ));

    // Start the animation
    _animationController.forward();

    // Add listener for amount validation
    _amountController.addListener(_validateAmount);

    // Focus listener for UI updates
    _focusNode.addListener(() {
      setState(() {
        // Trigger rebuild for focus-dependent styling
      });
    });
  }

  @override
  void dispose() {
    _amountController.removeListener(_validateAmount);
    _amountController.dispose();
    _promoCodeController.dispose();
    _animationController.dispose();
    _focusNode.dispose();
    super.dispose();
  }



  // Smooth, professional validation without jarring feedback
  void _validateAmount() {
    final text = _amountController.text.trim();

    if (text.isEmpty) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = null;
      });
      return;
    }

    final amount = double.tryParse(text);

    if (amount == null) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = 'Please enter a valid number';
      });
      return;
    }

    if (amount < minimumAmount) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = null; // Don't show error immediately for better UX
      });
      return;
    }

    if (amount > maximumAmount) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = 'Maximum amount is ₹${maximumAmount.toInt()}';
      });
      return;
    }

    // Amount is valid
    setState(() {
      _errorMessage = null;

      // Check if matches quick amount
      final amountInt = amount.toInt();
      if (amount == amountInt && _quickAmounts.contains(amountInt)) {
        _selectedAmount = amountInt;
      } else {
        _selectedAmount = null;
      }
    });
  }

  // Handle quick amount selection with smooth animation
  void _selectQuickAmount(int amount) {
    setState(() {
      _selectedAmount = amount;
      _amountController.text = amount.toString();
      _errorMessage = null;
    });

    // Smooth unfocus
    _focusNode.unfocus();
  }

  // Professional validation and submission with inline feedback
  void _handleAddBalance() {
    final text = _amountController.text.trim();

    if (text.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter an amount';
      });
      return;
    }

    final amount = double.tryParse(text);

    if (amount == null) {
      setState(() {
        _errorMessage = 'Please enter a valid amount';
      });
      return;
    }

    if (amount < minimumAmount) {
      setState(() {
        _errorMessage = 'Minimum amount is ₹${minimumAmount.toInt()}';
      });
      return;
    }

    if (amount > maximumAmount) {
      setState(() {
        _errorMessage = 'Maximum amount is ₹${maximumAmount.toInt()}';
      });
      return;
    }

    // Clear any error messages
    setState(() {
      _errorMessage = null;
    });

    // Calculate final amount including promo bonus
    final finalAmount = amount + _promoBonus;

    // Success - proceed with payment (server will determine payment gateway)
    // Pass both original amount and promo details
    widget.onAddBalance(
      amount, // Original amount for payment
      source: _serverSelectedGateway,
      // TODO: Add promo code details to callback if needed
      // promoCode: _appliedPromoCode,
      // promoBonus: _promoBonus,
      // finalAmount: finalAmount,
    );
  }



  // Get input background color with enhanced contrast
  Color _getInputBackgroundColor(bool isDarkMode) {
    if (_focusNode.hasFocus) {
      return const Color(0xFF4776E6).withValues(alpha: 0.05); // Use blue background when focused
    }
    // Default background colors for non-focused state
    return isDarkMode ? AppThemes.darkCard : Colors.grey.shade50;
  }

  // Build custom input box for amount entry with solid blue border
  Widget _buildCustomInputBox(bool isDarkMode) {
    return Container(
      height: 58, // Fixed height
      decoration: BoxDecoration(
        color: _getInputBackgroundColor(isDarkMode),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _focusNode.hasFocus
              ? const Color(0xFF4776E6) // Solid blue when focused
              : (isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300), // Gray when not focused
          width: 2.0, // Consistent border width
        ),
      ),
      child: Row(
        children: [
          // Rupee symbol container
          Container(
            width: 60,
            alignment: Alignment.center,
            child: Text(
              '₹',
              style: TextStyle(
                color: _focusNode.hasFocus
                    ? const Color(0xFF4776E6) // Blue when focused
                    : (isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600), // Gray when not focused
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Text input field
          Expanded(
            child: TextField(
              controller: _amountController,
              focusNode: _focusNode,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
              style: TextStyle(
                color: isDarkMode ? AppThemes.darkTextPrimary : const Color(0xFF1A1A1A),
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              decoration: InputDecoration(
                hintText: 'Enter amount (₹${minimumAmount.toInt()} or more)',
                hintStyle: TextStyle(
                  color: isDarkMode ? Colors.grey.shade500 : Colors.grey.shade500,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none, // Remove all borders
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedErrorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 0,
                  vertical: 16,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16), // Right padding
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    // Use app's primary lime color consistently
    final primaryColor = AppThemes.primaryColor; // Lime green #8cc051
    final secondaryColor = AppThemes.secondaryColor; // Blue #3D7AF5
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final screenHeight = MediaQuery.of(context).size.height;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: screenHeight * 0.9,
          ),
          decoration: BoxDecoration(
            color: isDarkMode ? AppThemes.darkSurface : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(28)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: isDarkMode ? 0.3 : 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Scrollable content area
                Flexible(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.only(
                      left: 24,
                      right: 24,
                      top: 16,
                      bottom: keyboardHeight > 0 ? 16 : 0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Modern sheet handle
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: Center(
                            child: Container(
                              width: 48,
                              height: 4,
                              decoration: BoxDecoration(
                                color: isDarkMode
                                    ? Colors.grey.shade600
                                    : Colors.grey.shade400,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 28),

                        // Professional title
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              children: [
                                Text(
                                  'Add Balance',
                                  style: TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.w700,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF1A1A1A),
                                    letterSpacing: -0.5,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Add money to your wallet securely',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: isDarkMode
                                        ? AppThemes.darkTextSecondary
                                        : Colors.grey.shade600,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 32),

                        // Professional amount input
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Enter Amount',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF333333),
                                  ),
                                ),
                                const SizedBox(height: 12),
                                // Custom input box with solid blue border when focused
                                _buildCustomInputBox(isDarkMode),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 28),

                        // Modern quick amount selection
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Quick Select',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? AppThemes.darkTextPrimary
                                        : const Color(0xFF333333),
                                  ),
                                ),
                                const SizedBox(height: 12),
                                Wrap(
                                  spacing: 12,
                                  runSpacing: 12,
                                  children: _quickAmounts.map((amount) {
                                    final isSelected =
                                        _selectedAmount == amount;
                                    return _buildModernQuickAmountButton(
                                      amount: amount,
                                      isSelected: isSelected,
                                      onTap: () => _selectQuickAmount(amount),
                                      primaryColor: primaryColor,
                                      secondaryColor: secondaryColor,
                                      isDarkMode: isDarkMode,
                                    );
                                  }).toList(),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 32),

                        // Promo code section - always visible
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: _buildPromoCodeSection(isDarkMode, primaryColor, secondaryColor),
                          ),
                        ),

                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ),

                // Fixed bottom section with validation and button
                Container(
                  padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
                  decoration: BoxDecoration(
                    color: isDarkMode ? AppThemes.darkSurface : Colors.white,
                    border: Border(
                      top: BorderSide(
                        color: isDarkMode
                            ? AppThemes.darkBorder
                            : Colors.grey.shade200,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Validation message area (always present but conditionally visible)
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        height: _errorMessage != null ? 40 : 0,
                        child: _errorMessage != null
                            ? Container(
                                width: double.infinity,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  color: isDarkMode
                                      ? Colors.red.shade900
                                          .withValues(alpha: 0.2)
                                      : Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.red.shade400,
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.warning_amber_rounded,
                                      color: Colors.red.shade600,
                                      size: 18,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        _errorMessage!,
                                        style: TextStyle(
                                          color: isDarkMode
                                              ? Colors.red.shade300
                                              : Colors.red.shade700,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : const SizedBox.shrink(),
                      ),

                      const SizedBox(height: 16),

                      // Show total amount if promo is applied
                      if (_isPromoApplied && _promoBonus > 0)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                primaryColor.withValues(alpha: 0.1),
                                secondaryColor.withValues(alpha: 0.1),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: primaryColor.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Payment Amount: ₹${_amountController.text}',
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? AppThemes.darkTextSecondary
                                          : Colors.grey.shade600,
                                      fontSize: 14,
                                    ),
                                  ),
                                  Text(
                                    'Bonus Amount: +₹${_promoBonus.toStringAsFixed(2)}',
                                    style: TextStyle(
                                      color: Colors.green.shade600,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    'Total Balance',
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? AppThemes.darkTextSecondary
                                          : Colors.grey.shade600,
                                      fontSize: 12,
                                    ),
                                  ),
                                  Text(
                                    '₹${(double.tryParse(_amountController.text) ?? 0 + _promoBonus).toStringAsFixed(2)}',
                                    style: TextStyle(
                                      color: isDarkMode
                                          ? AppThemes.darkTextPrimary
                                          : const Color(0xFF1A1A1A),
                                      fontSize: 18,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                      const SizedBox(height: 16),

                      // Always visible add balance button
                      SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: _buildModernAddBalanceButton(
                              primaryColor, secondaryColor, isDarkMode),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Modern quick amount button with professional styling
  Widget _buildModernQuickAmountButton({
    required int amount,
    required bool isSelected,
    required VoidCallback onTap,
    required Color primaryColor,
    required Color secondaryColor,
    required bool isDarkMode,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
        padding: const EdgeInsets.symmetric(
            horizontal: 18,
            vertical: 10), // Reduced padding for more compact design
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [primaryColor, secondaryColor],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected
              ? null
              : (isDarkMode ? AppThemes.darkCard : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Colors.transparent
                : (isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300),
            width: 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Text(
          '₹$amount',
          style: TextStyle(
            color: isSelected
                ? Colors.white
                : (isDarkMode
                    ? AppThemes.darkTextPrimary
                    : const Color(0xFF333333)),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }





  // Modern add balance button - always visible with professional styling
  Widget _buildModernAddBalanceButton(
      Color primaryColor, Color secondaryColor, bool isDarkMode) {
    // Button is always enabled but shows different states
    final canProceed = _amountController.text.isNotEmpty;

return Container(
      height: 52, // Reduced from 56 to 52 for more compact design
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [secondaryColor, secondaryColor], // Use only blue
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handleAddBalance(),
          borderRadius: BorderRadius.circular(16),
          splashColor: Colors.white.withValues(alpha: 0.2),
          highlightColor: Colors.white.withValues(alpha: 0.1),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (!canProceed) ...[
                  Icon(
                    Icons.info_outline,
                    color: Colors.white.withValues(alpha: 0.8),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  canProceed ? 'Add Balance' : 'Enter Amount to Continue',
                  style: TextStyle(
                    color: canProceed
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.9),
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    letterSpacing: -0.2,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Get appropriate icon based on offer status and type
  IconData _getOfferIcon(bool hasServerOffer, bool offerStatus) {
    if (hasServerOffer && offerStatus) {
      return Icons.local_offer_rounded; // Active server offer
    } else if (hasServerOffer && !offerStatus) {
      return Icons.discount_outlined; // Inactive server offer
    } else {
      return Icons.confirmation_number_outlined; // Default promo code icon
    }
  }

  // Build promo code section with professional UI
  Widget _buildPromoCodeSection(bool isDarkMode, Color primaryColor, Color secondaryColor) {
    // Always show promo code section, use server offer message if available
    final hasServerOffer = widget.offerMessage != null;
    final offerTitle = hasServerOffer
        ? (widget.offerMessage!['title'] ?? 'Available Offers')
        : 'Promo Codes & Offers';
    final offerMessage = hasServerOffer
        ? (widget.offerMessage!['message'] ?? 'Add money to get extra balance')
        : 'Enter a promo code to get extra balance on your recharge';

    // Extract offer status for visual indication
    final offerStatus = hasServerOffer ? (widget.offerMessage!['status'] ?? false) : false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Enhanced offer banner card with improved visual design
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            // Enhanced gradient with lime color theme
            gradient: LinearGradient(
              colors: [
                primaryColor.withValues(alpha: 0.08),
                primaryColor.withValues(alpha: 0.12),
                secondaryColor.withValues(alpha: 0.06),
              ],
              stops: const [0.0, 0.6, 1.0],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: primaryColor.withValues(alpha: 0.25),
              width: 1.5,
            ),
            // Subtle shadow for depth
            boxShadow: [
              BoxShadow(
                color: primaryColor.withValues(alpha: 0.08),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              // Enhanced icon container with better visual hierarchy
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      primaryColor,
                      primaryColor.withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  // Enhanced shadow for the icon container
                  boxShadow: [
                    BoxShadow(
                      color: primaryColor.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  _getOfferIcon(hasServerOffer, offerStatus),
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Enhanced title with better typography
                    Text(
                      offerTitle,
                      style: TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.w700,
                        color: isDarkMode
                            ? AppThemes.darkTextPrimary
                            : const Color(0xFF1A1A1A),
                        letterSpacing: -0.2,
                      ),
                    ),
                    const SizedBox(height: 6),
                    // Enhanced message with better readability
                    Text(
                      offerMessage,
                      style: TextStyle(
                        fontSize: 14,
                        color: isDarkMode
                            ? AppThemes.darkTextSecondary
                            : Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                        height: 1.3,
                      ),
                    ),
                    // Add status indicator if server offer is active
                    if (hasServerOffer && offerStatus) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: primaryColor.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: primaryColor.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.verified_rounded,
                              color: primaryColor,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Active Offer',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Add a subtle arrow indicator
              Icon(
                Icons.keyboard_arrow_down_rounded,
                color: primaryColor.withValues(alpha: 0.6),
                size: 24,
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Promo code input section
        AnimatedSize(
          duration: const Duration(milliseconds: 300),
          child: Column(
            children: [
              // Toggle button for promo code
              if (!_showPromoCode)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _showPromoCode = true;
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    decoration: BoxDecoration(
                      // Enhanced background with lime theme
                      color: isDarkMode
                          ? AppThemes.darkCard.withValues(alpha: 0.6)
                          : primaryColor.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(
                        color: primaryColor.withValues(alpha: 0.2),
                        width: 1.5,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.redeem_rounded, // More appropriate icon for promo codes
                          color: primaryColor,
                          size: 22,
                        ),
                        const SizedBox(width: 10),
                        Text(
                          'Have a promo code?',
                          style: TextStyle(
                            color: primaryColor,
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            letterSpacing: -0.1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Promo code input field with inline apply button
              if (_showPromoCode)
                Column(
                  children: [
                    const SizedBox(height: 16),

                    // Horizontal row with input field and apply button
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Promo code input field (takes most of the space)
                        Expanded(
                          flex: 3,
                          child: TextField(
                            controller: _promoCodeController,
                            enabled: !_isPromoApplied,
                            textCapitalization: TextCapitalization.characters,
                            style: TextStyle(
                              color: isDarkMode
                                  ? AppThemes.darkTextPrimary
                                  : const Color(0xFF1A1A1A),
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                            decoration: InputDecoration(
                              hintText: 'Enter promo code',
                              hintStyle: TextStyle(
                                color: isDarkMode
                                    ? Colors.grey.shade500
                                    : Colors.grey.shade500,
                                fontSize: 14,
                              ),
                              filled: true,
                              fillColor: isDarkMode
                                  ? AppThemes.darkCard
                                  : Colors.grey.shade50,
                              prefixIcon: Icon(
                                Icons.confirmation_number_outlined,
                                color: _isPromoApplied
                                    ? Colors.green
                                    : (isDarkMode
                                        ? Colors.grey.shade500
                                        : Colors.grey.shade600),
                                size: 20,
                              ),
                              suffixIcon: _isPromoApplied
                                  ? Icon(
                                      Icons.check_circle,
                                      color: Colors.green,
                                      size: 20,
                                    )
                                  : null,
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: _isPromoApplied
                                      ? Colors.green
                                      : (isDarkMode
                                          ? AppThemes.darkBorder
                                          : Colors.grey.shade300),
                                  width: 1.5,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: _isPromoApplied
                                      ? Colors.green
                                      : (isDarkMode
                                          ? AppThemes.darkBorder
                                          : AppThemes.primaryColor),
                                  width: 1.5,
                                ),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 14,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: _isPromoApplied
                                      ? Colors.green
                                      : (isDarkMode
                                          ? AppThemes.darkBorder
                                          : Colors.grey.shade300),
                                  width: 1.5,
                                ),
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(width: 12),

                        // Compact Apply/Remove button with enhanced responsiveness
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _isPromoLoading
                                  ? null
                                  : (_isPromoApplied ? _removePromoCode : _applyPromoCode),
                              borderRadius: BorderRadius.circular(12),
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                height: 52, // Match input field height
                                width: _isPromoLoading ? 52 : 80, // Square when loading, compact when not
                                decoration: BoxDecoration(
                                  gradient: _isPromoApplied
                                      ? LinearGradient(
                                          colors: [
                                            Colors.red.shade400,
                                            Colors.red.shade600,
                                          ],
                                        )
                                      : LinearGradient(
                                          colors: [primaryColor, primaryColor],
                                        ),
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: (_isPromoApplied ? Colors.red : primaryColor)
                                          .withValues(alpha: 0.3),
                                      blurRadius: _isPromoLoading ? 4 : 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Center(
                                  child: _isPromoLoading
                                      ? SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                          ),
                                        )
                                      : Text(
                                          _isPromoApplied ? 'Remove' : 'Apply',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 13,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Promo applied success message
                    if (_isPromoApplied && _promoBonus > 0)
                      Container(
                        margin: const EdgeInsets.only(top: 12),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.green.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.celebration_rounded,
                              color: Colors.green.shade700,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Promo Applied Successfully!',
                                    style: TextStyle(
                                      color: Colors.green.shade700,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    'You will get ₹${_promoBonus.toStringAsFixed(2)} extra (${_promoPercentage.toStringAsFixed(0)}% bonus)',
                                    style: TextStyle(
                                      color: Colors.green.shade600,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
            ],
          ),
        ),
      ],
    );
  }

  // Apply promo code logic with server validation
  Future<void> _applyPromoCode() async {
    final promoCode = _promoCodeController.text.trim().toUpperCase();
    if (promoCode.isEmpty) {
      return;
    }

    final amountText = _amountController.text.trim();
    if (amountText.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter amount first';
      });
      return;
    }

    final amount = double.tryParse(amountText) ?? 0;
    if (amount < minimumAmount) {
      setState(() {
        _errorMessage = 'Minimum amount is ₹${minimumAmount.toInt()}';
      });
      return;
    }

    // Show loading state
    setState(() {
      _errorMessage = null;
      _isPromoLoading = true;
    });

    try {
      // TODO: Replace with actual server validation API call
      // For now, implement client-side validation with common promo codes
      final promoResult = await _validatePromoCodeWithServer(promoCode, amount);

      if (promoResult['valid'] == true) {
        setState(() {
          _isPromoApplied = true;
          _appliedPromoCode = promoCode;
          _promoPercentage = promoResult['percentage'] ?? 15.0;
          _promoBonus = promoResult['bonus'] ?? (amount * _promoPercentage / 100);
          _errorMessage = null;
          _isPromoLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = promoResult['message'] ?? 'Invalid promo code';
          _isPromoLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to validate promo code. Please try again.';
        _isPromoLoading = false;
      });
    }
  }

  // Server-side promo code validation using your server's offer message format
  Future<Map<String, dynamic>> _validatePromoCodeWithServer(String promoCode, double amount) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    // TODO: Replace with actual API call to your server
    // Example API call:
    // final response = await http.post(
    //   Uri.parse('${ApiConstants.baseUrl}/validate-promo'),
    //   headers: {'Content-Type': 'application/json'},
    //   body: jsonEncode({
    //     'promo_code': promoCode,
    //     'amount': amount,
    //   }),
    // );

    // Extract percentage and minimum amount from server's offer message
    double serverPercentage = 15.0; // Default
    double serverMinAmount = 500.0; // Default

    if (widget.offerMessage != null && widget.offerMessage!['message'] != null) {
      final message = widget.offerMessage!['message'] as String;

      // Extract percentage from message like "Add ₹500 or more and get upto 15% extra balance"
      final percentageMatch = RegExp(r'(\d+)%').firstMatch(message);
      if (percentageMatch != null) {
        serverPercentage = double.tryParse(percentageMatch.group(1) ?? '15') ?? 15.0;
      }

      // Extract minimum amount from message
      final amountMatch = RegExp(r'₹(\d+)').firstMatch(message);
      if (amountMatch != null) {
        serverMinAmount = double.tryParse(amountMatch.group(1) ?? '500') ?? 500.0;
      }
    }

    // Sample promo codes that work with your server's offer format
    final samplePromoCodes = {
      'WELCOME10': {'percentage': 10.0, 'minAmount': 100.0, 'maxBonus': 50.0},
      'SAVE15': {'percentage': serverPercentage, 'minAmount': serverMinAmount, 'maxBonus': 100.0},
      'BONUS20': {'percentage': 20.0, 'minAmount': 1000.0, 'maxBonus': 200.0},
      'FIRST25': {'percentage': 25.0, 'minAmount': 200.0, 'maxBonus': 75.0},
      'SERVER15': {'percentage': serverPercentage, 'minAmount': serverMinAmount, 'maxBonus': 150.0},
    };

    if (samplePromoCodes.containsKey(promoCode)) {
      final promoData = samplePromoCodes[promoCode]!;
      final minAmount = promoData['minAmount'] as double;
      final percentage = promoData['percentage'] as double;
      final maxBonus = promoData['maxBonus'] as double;

      if (amount >= minAmount) {
        final calculatedBonus = (amount * percentage / 100);
        final finalBonus = calculatedBonus > maxBonus ? maxBonus : calculatedBonus;

        return {
          'valid': true,
          'percentage': percentage,
          'bonus': finalBonus,
          'message': 'Promo code applied successfully! You get ${percentage.toStringAsFixed(0)}% extra balance.'
        };
      } else {
        return {
          'valid': false,
          'message': 'Minimum amount ₹${minAmount.toInt()} required for this promo code'
        };
      }
    } else {
      return {
        'valid': false,
        'message': 'Invalid promo code. Please check and try again.'
      };
    }
  }

  // Remove promo code
  void _removePromoCode() {
    setState(() {
      _isPromoApplied = false;
      _appliedPromoCode = null;
      _promoBonus = 0.0;
      _promoPercentage = 0.0;
      _promoCodeController.clear();
    });
  }
}
